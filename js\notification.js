// Enhanced Notification Management System with Backend Integration
class NotificationManager {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.notifications = [];
        this.currentFilter = 'all';
        this.stats = {};
        this.isLoading = false;
        this.init();
    }

    async init() {
        this.bindEvents();
        await this.loadBackendData();

        // Ensure we always have notifications to display
        if (this.notifications.length === 0) {
            console.log('🔔 No notifications loaded, using fallback data');
            this.loadFallbackData();
        }
    }

    // Backend connectivity methods
    async loadBackendData() {
        try {
            console.log('🔔 Loading notifications from backend...');
            this.showLoadingState();

            // Load notifications and stats in parallel using shared utility
            const endpoints = [
                { url: '/notifications', fallback: this.getFallbackNotifications() },
                { url: '/notifications/stats', fallback: this.getFallbackStats() }
            ];

            const results = await window.bansheeUtils.fetchMultiple(endpoints);

            // Process notifications
            if (results[0].status === 'fulfilled') {
                const notificationData = results[0].value;
                this.notifications = notificationData.notifications || [];
                this.updateFilterCounts(notificationData.categories || []);
            }

            // Process stats
            if (results[1].status === 'fulfilled') {
                this.stats = results[1].value;
                this.updateStatsDisplay();
            }

            this.renderNotifications();
            console.log('✅ Notifications loaded successfully!');
            this.announceAction('Notifications updated');

        } catch (error) {
            console.error('❌ Error loading notifications:', error);
            console.log('🔄 Loading demo notifications...');
            this.loadFallbackData();
            this.announceAction('Using demo notifications - server unavailable');
        } finally {
            this.hideLoadingState();
        }
    }

    loadFallbackData() {
        this.notifications = this.getFallbackNotifications();
        this.stats = this.getFallbackStats();
        this.renderNotifications();
    }

    getFallbackNotifications() {
        return [
            {
                id: 1,
                type: 'new_release',
                title: 'New Release Alert',
                message: 'Your favorite artist "Cosmic Waves" just released a new album "Stellar Dreams"',
                time: '2 minutes ago',
                unread: true,
                icon: 'fas fa-music',
                category: 'music',
                priority: 'high'
            },
            {
                id: 2,
                type: 'playlist_update',
                title: 'Playlist Shared',
                message: 'Alex shared their "Chill Vibes" playlist with you',
                time: '15 minutes ago',
                unread: true,
                icon: 'fas fa-share',
                category: 'social',
                priority: 'medium'
            },
            {
                id: 3,
                type: 'recommendation',
                title: 'Weekly Discovery',
                message: 'Your personalized "Discover Weekly" playlist is ready with 30 new tracks',
                time: '1 hour ago',
                unread: false,
                icon: 'fas fa-compass',
                category: 'discovery',
                priority: 'medium'
            },
            {
                id: 4,
                type: 'subscription',
                title: 'Premium Subscription',
                message: 'Your premium subscription will renew in 3 days',
                time: '2 hours ago',
                unread: false,
                icon: 'fas fa-crown',
                category: 'billing',
                priority: 'medium'
            },
            {
                id: 5,
                type: 'concert_alert',
                title: 'Concert Near You',
                message: 'Dua Lipa is performing in your city next month!',
                time: '6 hours ago',
                unread: true,
                icon: 'fas fa-calendar-alt',
                category: 'music',
                priority: 'high'
            },
            {
                id: 6,
                type: 'achievement',
                title: 'Achievement Unlocked',
                message: 'You\'ve listened to 100 hours of music this month!',
                time: '1 day ago',
                unread: false,
                icon: 'fas fa-trophy',
                category: 'system',
                priority: 'medium'
            },
            {
                id: 7,
                type: 'friend_activity',
                title: 'Friend Activity',
                message: 'Sarah liked your playlist "Summer Hits 2024"',
                time: '2 days ago',
                unread: false,
                icon: 'fas fa-heart',
                category: 'social',
                priority: 'low'
            },
            {
                id: 8,
                type: 'system_update',
                title: 'App Update Available',
                message: 'Version 2.1.0 is now available with new features',
                time: '3 days ago',
                unread: false,
                icon: 'fas fa-download',
                category: 'system',
                priority: 'medium'
            }
        ];
    }

    getFallbackStats() {
        return {
            total: 8,
            unread: 3,
            byCategory: {
                music: 3,
                social: 2,
                system: 2,
                discovery: 1
            }
        };
    }

    bindEvents() {
        // Filter tabs
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // Action buttons
        document.getElementById('markAllReadBtn').addEventListener('click', () => {
            this.markAllAsRead();
        });

        document.getElementById('clearAllBtn').addEventListener('click', () => {
            this.clearAllNotifications();
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.clearSelection();
            }
        });
    }

    setFilter(filter) {
        this.currentFilter = filter;

        // Update active tab
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        this.renderNotifications();
        this.announceFilterChange(filter);
    }

    getFilteredNotifications() {
        if (this.currentFilter === 'all') {
            return this.notifications;
        }
        return this.notifications.filter(notification => notification.type === this.currentFilter);
    }

    renderNotifications() {
        const container = document.getElementById('notificationsList');
        const emptyState = document.getElementById('emptyState');
        const filteredNotifications = this.getFilteredNotifications();

        if (filteredNotifications.length === 0) {
            container.innerHTML = '';
            emptyState.classList.remove('hidden');
            return;
        }

        emptyState.classList.add('hidden');

        container.innerHTML = filteredNotifications.map(notification =>
            this.createNotificationHTML(notification)
        ).join('');

        // Add event listeners to notification items
        this.bindNotificationEvents();
    }

    createNotificationHTML(notification) {
        const unreadClass = notification.unread ? 'unread' : '';

        return `
            <div class="notification-item ${unreadClass}" data-id="${notification.id}" tabindex="0">
                <div class="notification-header">
                    <div style="display: flex; align-items: flex-start;">
                        <div class="notification-icon ${notification.type}">
                            <i class="${notification.icon}"></i>
                        </div>
                        <div class="notification-content">
                            <h3 class="notification-title">${notification.title}</h3>
                            <p class="notification-message">${notification.message}</p>
                        </div>
                    </div>
                </div>
                <div class="notification-meta">
                    <span class="notification-time">${notification.time}</span>
                    <div class="notification-actions">
                        ${notification.unread ? '<button type="button" class="notification-action mark-read" data-id="' + notification.id + '">Mark as Read</button>' : ''}
                        <button type="button" class="notification-action delete" data-id="${notification.id}">Delete</button>
                    </div>
                </div>
            </div>
        `;
    }

    getIconClass(type) {
        const iconMap = {
            music: 'music',
            social: 'social',
            system: 'system'
        };
        return iconMap[type] || 'system';
    }

    bindNotificationEvents() {
        // Click to mark as read
        document.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.classList.contains('notification-action')) {
                    const id = parseInt(item.dataset.id);
                    this.markAsRead(id);
                }
            });

            // Keyboard support
            item.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const id = parseInt(item.dataset.id);
                    this.markAsRead(id);
                }
            });
        });

        // Action buttons
        document.querySelectorAll('.notification-action.mark-read').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.markAsRead(id);
            });
        });

        document.querySelectorAll('.notification-action.delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.deleteNotification(id);
            });
        });
    }

    async markAsRead(id) {
        try {
            // Update backend first
            const response = await fetch(`${this.apiBase}/notifications/${id}/read`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' }
            });

            if (response.ok) {
                // Update local state
                const notification = this.notifications.find(n => n.id === id);
                if (notification && notification.unread) {
                    notification.unread = false;
                    this.renderNotifications();
                    this.announceAction(`Notification "${notification.title}" marked as read`);
                }
            } else {
                throw new Error('Failed to mark notification as read');
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
            // Fallback to local update
            const notification = this.notifications.find(n => n.id === id);
            if (notification && notification.unread) {
                notification.unread = false;
                this.renderNotifications();
                this.announceAction(`Notification marked as read (offline mode)`);
            }
        }
    }

    async deleteNotification(id) {
        try {
            // Update backend first
            const response = await fetch(`${this.apiBase}/notifications/${id}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                // Update local state with animation
                const notification = this.notifications.find(n => n.id === id);
                if (notification) {
                    const element = document.querySelector(`[data-id="${id}"]`);
                    if (element) {
                        element.classList.add('slide-out');
                        setTimeout(() => {
                            this.notifications = this.notifications.filter(n => n.id !== id);
                            this.renderNotifications();
                            this.announceAction(`Notification "${notification.title}" deleted`);
                        }, 300);
                    }
                }
            } else {
                throw new Error('Failed to delete notification');
            }
        } catch (error) {
            console.error('Error deleting notification:', error);
            // Fallback to local delete
            const notification = this.notifications.find(n => n.id === id);
            if (notification) {
                const element = document.querySelector(`[data-id="${id}"]`);
                if (element) {
                    element.classList.add('slide-out');
                    setTimeout(() => {
                        this.notifications = this.notifications.filter(n => n.id !== id);
                        this.renderNotifications();
                        this.announceAction(`Notification deleted (offline mode)`);
                    }, 300);
                }
            }
        }
    }

    async markAllAsRead() {
        const unreadCount = this.notifications.filter(n => n.unread).length;
        if (unreadCount === 0) {
            this.announceAction('No unread notifications to mark');
            return;
        }

        try {
            // Update backend first
            const response = await fetch(`${this.apiBase}/notifications/read-all`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    category: this.currentFilter === 'all' ? null : this.currentFilter
                })
            });

            if (response.ok) {
                const result = await response.json();
                // Update local state
                this.notifications.forEach(notification => {
                    if (this.currentFilter === 'all' || notification.category === this.currentFilter) {
                        notification.unread = false;
                    }
                });

                this.renderNotifications();
                this.announceAction(result.message || `${unreadCount} notifications marked as read`);
            } else {
                throw new Error('Failed to mark all notifications as read');
            }
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
            // Fallback to local update
            this.notifications.forEach(notification => {
                notification.unread = false;
            });

            this.renderNotifications();
            this.announceAction(`${unreadCount} notifications marked as read (offline mode)`);
        }
    }

    clearAllNotifications() {
        if (this.notifications.length === 0) {
            this.announceAction('No notifications to clear');
            return;
        }

        const count = this.notifications.length;

        // Show confirmation dialog
        if (confirm(`Are you sure you want to delete all ${count} notifications? This action cannot be undone.`)) {
            this.notifications = [];
            this.renderNotifications();
            this.announceAction(`All ${count} notifications cleared`);
        }
    }

    clearSelection() {
        // Remove focus from any focused notification
        document.activeElement.blur();
    }

    announceFilterChange(filter) {
        const filterNames = {
            all: 'All notifications',
            music: 'Music notifications',
            social: 'Social notifications',
            system: 'System notifications'
        };

        const count = this.getFilteredNotifications().length;
        const message = `Showing ${filterNames[filter]}. ${count} notification${count !== 1 ? 's' : ''} found.`;
        this.announceAction(message);
    }

    announceAction(message) {
        const liveRegion = document.getElementById('aria-live-region');
        liveRegion.textContent = message;

        // Clear the message after a short delay to allow for new announcements
        setTimeout(() => {
            liveRegion.textContent = '';
        }, 1000);
    }

    // Method to add new notifications (for future use)
    addNotification(notification) {
        const newId = Math.max(...this.notifications.map(n => n.id), 0) + 1;
        const newNotification = {
            id: newId,
            unread: true,
            time: 'Just now',
            ...notification
        };

        this.notifications.unshift(newNotification);
        this.renderNotifications();

        // Add slide-in animation to new notification
        setTimeout(() => {
            const element = document.querySelector(`[data-id="${newId}"]`);
            if (element) {
                element.classList.add('slide-in');
            }
        }, 100);
    }

    // Get unread count (for future use with badge)
    getUnreadCount() {
        return this.notifications.filter(n => n.unread).length;
    }

    // Loading state management
    showLoadingState() {
        const container = document.querySelector('.notifications-container');
        if (container) {
            container.innerHTML = `
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>Loading notifications...</p>
                </div>
            `;
        }
    }

    hideLoadingState() {
        // Loading state will be replaced by renderNotifications()
    }

    // Update filter counts based on backend data
    updateFilterCounts(categories) {
        categories.forEach(category => {
            const tab = document.querySelector(`[data-filter="${category}"]`);
            if (tab) {
                const count = this.notifications.filter(n =>
                    category === 'all' || n.category === category
                ).length;

                // Update tab text with count if needed
                if (count > 0 && category !== 'all') {
                    const badge = tab.querySelector('.count-badge') || document.createElement('span');
                    badge.className = 'count-badge';
                    badge.textContent = count;
                    if (!tab.querySelector('.count-badge')) {
                        tab.appendChild(badge);
                    }
                }
            }
        });
    }

    // Update stats display
    updateStatsDisplay() {
        if (!this.stats) return;

        // Update unread count in header
        const unreadCountElement = document.querySelector('.unread-count');
        if (unreadCountElement) {
            unreadCountElement.textContent = this.stats.unread;
        }

        // Update total count
        const totalCountElement = document.querySelector('.total-count');
        if (totalCountElement) {
            totalCountElement.textContent = this.stats.total;
        }
    }

    // Accessibility announcement
    announceAction(message) {
        if (window.bansheeUtils) {
            window.bansheeUtils.announceToScreenReader(message);
        } else {
            console.log('📢', message);
        }
    }
}

// Initialize the notification manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.notificationManager = new NotificationManager();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}